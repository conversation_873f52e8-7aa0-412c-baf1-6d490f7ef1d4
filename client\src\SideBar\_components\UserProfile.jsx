import { Store } from "react-data-stores";
import { UserToken } from "../../data";
import { getProfileRouteForRole, getProfileIconForRole } from "../../utils/profileRouting";

export default function UserProfile({ userData }) {
  return (
    <div className="flex items-center justify-between px-4 py-3 border-t border-white/10 bg-black/20">
      <div
        className="flex items-center gap-3 cursor-pointer hover:bg-white/5 rounded-lg p-2 transition-colors"
        onClick={() => {
          Store.navigateTo(getProfileRouteForRole(userData.data.role));
        }}
      >
        <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
          {getProfileIconForRole(userData.data.role)}
        </div>
        <span className="text-sm truncate max-w-[120px]">
          {userData.data.name?.join(" ") || "User"}
        </span>
      </div>
      <button
        className="w-8 h-8 flex items-center justify-center rounded-full text-white hover:bg-white/10 transition"
        onClick={() => {
          localStorage.removeItem(UserToken);
          window.location.href='/'
        }}
      >
        <i className="fa-solid fa-right-from-bracket"></i>
      </button>
    </div>
  );
}
