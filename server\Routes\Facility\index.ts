import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { User } from "../../Models/User";
import { roles } from "../../utils/roles";
import { FACILITY_ANALITICS_DAYS_RANGE, QUERY_PAGE_SIZE_LIMIT } from "../../utils/FLAGS";
import { signWorkerInviteToken, verifyWorkerInviteToken } from "../../utils/workerInviteToken";
import { sendInviteEmail } from "../../utils/mailer";
import { Facility } from "../../Models/Facility";
import { tryCatch } from "../../utils/TryCatch";
import { Visit } from "../../Models/Visit";
import { Types } from "mongoose";
import {
  createProfileImageUpload,
  validateUploadedFile,
  generateProfileImageFilename,
  saveProfileImage,
  deleteOldProfileImage
} from "../../utils/fileUpload";

const router = Router();

// Create multer upload instance for profile images
const profileImageUpload = createProfileImageUpload();

// Public route: GET /facility/workers/join?token=...
router.get("/workers/join", async (req, res) => {
  try {
    const { token } = req.query as { token?: string };
    if (!token) {
      res.status(400).json({ error: "Missing token" });
      return;
    }
    const payload = verifyWorkerInviteToken(token);
    const { userId, facilityId, role, specialization } = payload;

    if (![roles.admin, roles.doctor].includes(role)) {
      res.status(400).json({ error: "Invalid role in token" });
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const exists = user.profiles.find(
      (p) => p.type === role && p.hospital?.toString() === facilityId.toString()
    );
    if (exists) {
      res.json({ message: "Already joined" });
      return;
    }

    user.profiles.push({
      type: role,
      hospital: (facilityId as unknown) as any,
      specialization: role === roles.doctor ? (specialization || "") : "",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    user.updatedAt = Date.now();
    await user.save();

    res.json({ message: "Joined successfully" });
  } catch (e: any) {
    if (e?.name === "TokenExpiredError") {
      res.status(400).json({ error: "Token expired" });
      return;
    }
    res.status(400).json({ error: "Invalid token" });
  }
});

// Authenticated routes below


router.use(authMiddleware);

// Get facility manager profile
router.get("/profile", async (req, res) => {
  try {
    const facilityManagerId = req.user.id;

    const facilityManagerUser = await User.findById(facilityManagerId)
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!facilityManagerUser) {
      res.status(404).json({ message: "Facility manager not found" });
      return;
    }

    res.json({ data: facilityManagerUser });
  } catch (error) {
    console.error("Facility manager profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Update facility manager profile
router.put("/profile", async (req, res) => {
  try {
    const facilityManagerId = req.user.id;
    const { name, email, phone, img } = req.body;

    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (phone) updateData.phone = phone;
    if (img) updateData.img = img;
    updateData.updatedAt = Date.now();

    const updatedUser = await User.findByIdAndUpdate(
      facilityManagerId,
      updateData,
      { new: true }
    )
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ message: "Facility manager not found" });
      return;
    }

    res.json({ data: updatedUser });
  } catch (error) {
    console.error("Update facility manager profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Upload facility manager profile image
router.post("/profile/upload", profileImageUpload.single("image"), async (req, res) => {
  try {
    const facilityManagerId = req.user.id;
    const file = (req as any).file;

    // Validate the uploaded file
    const validationError = validateUploadedFile(file);
    if (validationError) {
      res.status(400).json({ error: validationError });
      return;
    }

    // Get current user to check for existing image
    const currentUser = await User.findById(facilityManagerId).select("img").lean();
    if (!currentUser) {
      res.status(404).json({ error: "Facility manager not found" });
      return;
    }

    // Generate filename and save file
    const filename = generateProfileImageFilename(facilityManagerId, file.originalname);
    const publicPath = await saveProfileImage(file, filename, 'users');

    // Update user's img field in database
    const updatedUser = await User.findByIdAndUpdate(
      facilityManagerId,
      {
        img: publicPath,
        updatedAt: Date.now()
      },
      { new: true }
    )
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ error: "Facility manager not found" });
      return;
    }

    // Delete old image file if it exists
    if (currentUser.img) {
      await deleteOldProfileImage(currentUser.img);
    }

    res.status(201).json({
      message: "Profile image uploaded successfully",
      imagePath: publicPath,
      user: updatedUser
    });

  } catch (error) {
    console.error("Upload facility manager profile image error:", error);
    res.status(500).json({ error: "Failed to upload profile image" });
  }
});

// Dashboard metrics for Facility Manager

router.get(
  "/analytics",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {

    const [FacilityManagerError, facilityManagerUser] = await tryCatch(User.findOne({
      _id: req.user?.id,
      is_deleted: { $ne: true },
      "profiles.type": { $in: [roles.facilityManager] },
    }).lean());
    if (FacilityManagerError) {
      res.status(500).json({ error: "Internal server error" });
      return;
    }
    if (!facilityManagerUser) {
      res.status(404).json({ error: "User not found" });
      return;
    }
    //! we have facility manager data via facilityManagerUser variable
    //@ts-ignore
    const hospitalId = facilityManagerUser.profiles.find(
      p => p.type === roles.facilityManager
    ).hospital;
    if (!hospitalId) {
      res.status(400).json({ error: "Facility manager not associated with a facility " });
      return;
    }
    const [HospitalError, hospital] = await tryCatch(Facility.findById(hospitalId).lean());
    if (HospitalError) {
      res.status(500).json({ error: "Internal server error" });
      return;
    }
    if (!hospital) {
      res.status(404).json({ error: "Hospital not found" });
      return;
    }
    //! we have hospital data via hospital variable
    const [PatientsAnalyticsError, patientsAnalytics] = await tryCatch(
      Visit.aggregate([
        {
          $match: {
            facility: new Types.ObjectId(hospitalId),
            is_deleted: { $ne: true },
            date: { $gte: Date.now() - FACILITY_ANALITICS_DAYS_RANGE * 24 * 60 * 60 * 1000 }
          }
        },

        {
          $lookup: {
            from: 'users',              // your users collection name
            localField: 'patient',
            foreignField: '_id',
            as: 'patient'
          }
        },

        { $unwind: '$patient' },

        {
          $facet: {
            countByGender: [
              {
                $group: {
                  _id: '$patient.gender',  // group by doctor gender
                  count: { $sum: 1 }
                }
              },
              {
                $project: {
                  _id: 0,
                  gender: '$_id',
                  count: 1
                }
              }
            ],

            totalLast7Days: [
              {
                $match: { createdAt: { $gte: Date.now() - 7 * 24 * 60 * 60 * 1000 } }
              },
              {
                $count: 'count'
              }
            ]
          }
        }
      ]) as Promise<{
        countByGender: Array<{ gender: string | null; count: number }>;
        totalLast7Days: Array<{ count: number }>;
      }[]>
    );
    if (PatientsAnalyticsError || !patientsAnalytics) {
      res.status(500).json({ error: "Internal server error" });
      return;
    }
    //! we have patients analytics via patientsAnalytics variable
    const [WorkersAnalyticsError, workersAnalytics] = await tryCatch(
      User.aggregate([
        {
          $match: {
            is_deleted: { $ne: true },
            profiles: {
              $elemMatch: {
                hospital: new Types.ObjectId(hospitalId),
                type: { $in: [roles.admin, roles.doctor] },
              }
            },
          }
        }
        ,
        { $unwind: "$profiles" }
        ,
        {
          $group: {
            _id: "$profiles.type", count: { $sum: 1 },
          }
        },
        {
          $project: {
            _id: 0,
            type: "$_id",
            count: 1
          }
        }
      ])
    )
    if (WorkersAnalyticsError || !workersAnalytics) {
      res.status(500).json({ error: "Internal server error" });
      return;
    }
    //! we have workers analytics via workersAnalytics variable
    const VisitsRecordYearDetails = (req.query.year ? (+req.query.year) : new Date().getFullYear()) as number;

    const [VisitsAnalyticsError, visitsAnalytics] = await tryCatch(
      Visit.aggregate([
        {
          $match: {
            facility: new Types.ObjectId(hospitalId),
            is_deleted: { $ne: true },
            date: {
              $gte: new Date(`${VisitsRecordYearDetails}-01-01T00:00:00Z`).getTime(),
              $lt: new Date(`${VisitsRecordYearDetails + 1}-01-01T00:00:00Z`).getTime(),
            }
          }
        },
        {
          $group: {
            _id: { $month: { $toDate: "$date" } },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            month: '$_id',  // rename _id to month for clarity

            _id: 0,       // hides the _id field (month number)
            count: 1      // includes only the count field
          }
        }
      ])
    );
    if (VisitsAnalyticsError || !visitsAnalytics) {
      res.status(500).json({ error: "Internal server error" });
      return;
    }
    //! we have medicalRecords analytics via visitsAnalytics variable

    res.json({
      patients: patientsAnalytics,
      staff: workersAnalytics,
      medicalRecords: visitsAnalytics,
      facility: hospital,
      facilityManager: facilityManagerUser,
    });
  }
);
// GET /facility/workers - Get workers (admin and doctor roles) for the facility
router.get(
  "/workers",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { page = 0, search = "", exact = "false" } = req.query;
      const pageNumber = parseInt(page as string);
      const isExact = exact === "true";

      // Get the facility manager's hospital from their profile
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Build search query for workers (admin and doctor roles only)
      const searchQuery: any = {
        is_deleted: { $ne: true },
        "profiles.type": { $in: [roles.admin, roles.doctor] },
        "profiles.hospital": facilityId
      };

      if (search) {
        const searchStr = search as string;
        if (isExact) {
          searchQuery.$or = [
            { cin: searchStr },
            { email: searchStr },
            { phone: searchStr }
          ];
        } else {
          searchQuery.$or = [
            { name: { $regex: searchStr, $options: "i" } },
            { cin: { $regex: searchStr, $options: "i" } },
            { email: { $regex: searchStr, $options: "i" } },
            { phone: { $regex: searchStr, $options: "i" } }
          ];
        }
      }

      const workers = await User.find(searchQuery)
        .skip(pageNumber * QUERY_PAGE_SIZE_LIMIT)
        .limit(QUERY_PAGE_SIZE_LIMIT)
        .select("-password")
        .lean();

      res.json(workers);
    } catch (error) {
      console.error("Error fetching workers:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// GET /facility/workers/search - Advanced search for workers
router.get(
  "/workers/search",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { cin, name, phone, email, role, specialization, exact = "false" } = req.query;
      const isExact = exact === "true";

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      const searchQuery: any = {
        is_deleted: { $ne: true },
        "profiles.type": { $in: [roles.admin, roles.doctor] },
        "profiles.hospital": facilityId
      };

      if (cin) {
        searchQuery.cin = isExact ? cin : { $regex: cin, $options: "i" };
      }
      if (name) {
        searchQuery.name = isExact ? name : { $regex: name, $options: "i" };
      }
      if (phone) {
        searchQuery.phone = isExact ? phone : { $regex: phone, $options: "i" };
      }
      if (email) {
        searchQuery.email = isExact ? email : { $regex: email, $options: "i" };
      }
      if (role && [roles.admin, roles.doctor].includes(role as string)) {
        searchQuery["profiles.type"] = role;
      }
      if (specialization) {
        searchQuery["profiles.specialization"] = isExact
          ? specialization
          : { $regex: specialization, $options: "i" };
      }

      const workers = await User.find(searchQuery)
        .select("-password")
        .lean();

      res.json(workers);
    } catch (error) {
      console.error("Error in advanced worker search:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// PUT /facility/me - Update facility details
router.put(
  "/me",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }
      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );
      if (!facilityManagerProfile?.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }
      const facilityId = facilityManagerProfile.hospital;

      const allowed: any = {};
      const { name, type, address, phone, img, is_deleted } = req.body;
      if (name !== undefined) allowed.name = name;
      if (type !== undefined) allowed.type = type; // enum validated by schema
      if (address !== undefined) allowed.address = address;
      if (phone !== undefined) allowed.phone = phone;
      if (img !== undefined) allowed.img = img;
      if (is_deleted !== undefined) allowed.is_deleted = is_deleted; // allow soft delete toggle
      allowed.updatedAt = Date.now();

      const updated = await Facility.findByIdAndUpdate(facilityId, allowed, { new: true }).lean();
      if (!updated) {
        res.status(404).json({ error: "Facility not found" });
        return;
      }
      res.json(updated);
    } catch (e) {
      console.error(e);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// POST /facility/me/upload - Upload facility image
router.post(
  "/me/upload",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  profileImageUpload.single("image"),
  async (req, res) => {
    try {
      const file = (req as any).file;

      // Validate the uploaded file
      const validationError = validateUploadedFile(file);
      if (validationError) {
        res.status(400).json({ error: validationError });
        return;
      }

      // Get facility manager and facility
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );
      if (!facilityManagerProfile?.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital.toString();

      // Get current facility to check for existing image
      const currentFacility = await Facility.findById(facilityId).select("img").lean();
      if (!currentFacility) {
        res.status(404).json({ error: "Facility not found" });
        return;
      }

      // Generate filename and save file
      const filename = generateProfileImageFilename(facilityId, file.originalname);
      const publicPath = await saveProfileImage(file, filename, 'facilities');

      // Update facility's img field in database
      const updatedFacility = await Facility.findByIdAndUpdate(
        facilityId,
        {
          img: publicPath,
          updatedAt: Date.now()
        },
        { new: true }
      ).lean();

      if (!updatedFacility) {
        res.status(404).json({ error: "Facility not found" });
        return;
      }

      // Delete old image file if it exists
      if (currentFacility.img) {
        await deleteOldProfileImage(currentFacility.img);
      }

      res.status(201).json({
        message: "Facility image uploaded successfully",
        imagePath: publicPath,
        facility: updatedFacility
      });

    } catch (error) {
      console.error("Upload facility image error:", error);
      res.status(500).json({ error: "Failed to upload facility image" });
    }
  }
);

// POST /facility/workers - Create a new worker (admin or doctor)
router.post(
  "/workers",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const {
        cin,
        firstName,
        lastName,
        email,
        phone,
        gender,
        birthDate,
        role,
        specialization,
        password
      } = req.body;

      // Validate required fields
      if (!cin || !firstName || !lastName || !email || !gender || !birthDate || !role || !password) {
        res.status(400).json({ error: "Missing required fields" });
        return;
      }

      // Validate role
      if (![roles.admin, roles.doctor].includes(role)) {
        res.status(400).json({ error: "Invalid role. Only admin and doctor roles are allowed" });
        return;
      }

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Check if user with this CIN or email already exists
      const existingUser = await User.findOne({
        $or: [{ cin }, { email }],
        is_deleted: { $ne: true }
      });

      if (existingUser) {
        const alreadyInFacility = existingUser.profiles.find(
          (p) => p.type === role && p.hospital?.toString() === facilityId.toString()
        );
        if (alreadyInFacility) {
          res.status(400).json({ error: "User already exists with this role in this facility" });
          return;
        }
        // Send invitation email with 48h token so the user can accept joining
        const token = signWorkerInviteToken({
          userId: existingUser._id.toString(),
          facilityId: facilityId.toString(),
          role: role,
          specialization: role === roles.doctor ? (specialization || "") : undefined,
          inviterId: req.user.id,
        });
        const baseUrl = process.env.APP_BASE_URL || `http://localhost:${process.env.APP_PORT || 4000}`;
        const joinUrl = `${baseUrl}/facility/workers/join?token=${encodeURIComponent(token)}`;
        // Fetch facility name for email context
        let facilityName = "Facility";
        try {
          const facility = await Facility.findById(facilityId).lean();
          if (facility?.name) facilityName = facility.name;
        } catch { }
        try {
          await sendInviteEmail({ to: existingUser.email, facilityName, joinUrl });
        } catch (e) {
          console.error("Failed to send invite email:", e);
          res.status(500).json({ error: "Failed to send invitation email" });
          return;
        }
        res.status(202).json({ message: "User exists. Invitation email sent.", email: existingUser.email });
        return;
      }

      // Create new user
      const newUser = new User({
        name: [firstName, lastName],
        email,
        phone: phone || null,
        img: null,
        gender,
        BirthDay: typeof birthDate === 'number' ? birthDate : new Date(birthDate).getTime(),
        password: require("../../utils/passwordHash").hashPassword(password),
        cin,
        profiles: [{
          type: role,
          hospital: facilityId,
          specialization: role === roles.doctor ? (specialization || "") : "",
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      await newUser.save();

      // Return the created user without password
      const userResponse = newUser.toObject();
      const { password: _, ...userWithoutPassword } = userResponse;

      res.status(201).json(userWithoutPassword);
    } catch (error) {
      console.error("Error creating worker:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// DELETE /facility/workers/:workerId - Soft delete a worker
router.delete(
  "/workers/:workerId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { workerId } = req.params;

      if (!workerId) {
        res.status(400).json({ error: "Worker ID is required" });
        return;
      }

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Find the worker and verify they belong to this facility
      const worker = await User.findById(workerId);
      if (!worker) {
        res.status(404).json({ error: "Worker not found" });
        return;
      }

      // Check if worker belongs to this facility and has admin/doctor role
      const workerProfile = worker.profiles.findIndex(
        p => (p.type === roles.admin || p.type === roles.doctor) &&
          p.hospital?.toString() === facilityId.toString()
      );

      if (workerProfile === -1) {
        res.status(403).json({ error: "Worker does not belong to your facility or is not a valid worker" });
        return;
      }

      worker.profiles.splice(workerProfile, 1);
      worker.updatedAt = Date.now();
      await worker.save();

      res.json({ message: "Worker deleted successfully" });
    } catch (error) {
      console.error("Error deleting worker:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.post("/", async (_req, res) => {
  res.status(501).json({ message: "Not implemented" });
});
export default router;
