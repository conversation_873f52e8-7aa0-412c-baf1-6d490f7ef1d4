import { Route, Routes } from "react-router-dom";
import IndexRouteFacility from "./IndexRoute/index";
import WorkersRoute from "./Workers";
import FacilityDashboard from "./Dashboard";
import FacilityManagerProfile from "./Profile";
import FacilitySettings from "./Settings";
import { _404 } from "../index";

export default function FacilityRoute() {
  return (
    <Routes>
      <Route index element={<IndexRouteFacility />} />
      <Route path="dashboard" element={<FacilityDashboard />} />
      <Route path="profile" element={<FacilityManagerProfile />} />
      <Route path="settings" element={<FacilitySettings />} />
      <Route path="workers/*" element={<WorkersRoute />} />
      <Route path="*" element={<_404 />} />
    </Routes>
  );
}
