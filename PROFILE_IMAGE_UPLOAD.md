# Profile Image Upload Implementation

This document describes the newly implemented profile image upload functionality for both user profiles and facility profiles.

## Overview

The implementation follows the existing medical record test image upload pattern and provides secure file upload functionality with proper validation, storage, and database integration.

## Features

- **File Storage**: Actual image files stored on server filesystem (not external links)
- **Naming Convention**: `[USERID/FACILITYID]_[UNIXTIMESTAMP].[ext]`
- **File Validation**: Type, size, and security checks
- **Automatic Cleanup**: Old images are deleted when new ones are uploaded
- **Multiple User Types**: Support for patients, doctors, admins, sudo users, and facility managers

## API Endpoints

### User Profile Image Upload

#### Patients
- **POST** `/patients/profile/upload`
- **Headers**: `Authorization: <token>`
- **Body**: `multipart/form-data` with `image` field

#### Doctors
- **POST** `/doctors/profile/upload`
- **Headers**: `Authorization: <token>`
- **Body**: `multipart/form-data` with `image` field

#### Admins
- **POST** `/admin/profile/upload`
- **Headers**: `Authorization: <token>`
- **Body**: `multipart/form-data` with `image` field

#### Sudo Users
- **POST** `/sudo/profile/upload`
- **Headers**: `Authorization: <token>`
- **Body**: `multipart/form-data` with `image` field

#### Facility Managers
- **POST** `/facility/profile/upload`
- **Headers**: `Authorization: <token>`
- **Body**: `multipart/form-data` with `image` field

### Facility Image Upload

#### Facility Logo/Image
- **POST** `/facility/me/upload`
- **Headers**: `Authorization: <token>`
- **Body**: `multipart/form-data` with `image` field
- **Access**: Only facility managers can upload facility images

## File Specifications

### Supported Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### File Limits
- **Maximum Size**: 5MB
- **Maximum Files**: 1 per upload

### Storage Structure
```
server/uploads/profiles/
├── users/           # User profile images
│   └── [USERID]_[TIMESTAMP].[ext]
└── facilities/      # Facility images
    └── [FACILITYID]_[TIMESTAMP].[ext]
```

## Client-Side API Functions

### JavaScript/React Usage

```javascript
import { 
  uploadUserProfileImageAPI, 
  uploadFacilityManagerProfileImageAPI, 
  uploadFacilityImageAPI 
} from './api';

// Upload user profile image
const [error, result] = await uploadUserProfileImageAPI('patient', file, token);

// Upload facility manager profile image
const [error, result] = await uploadFacilityManagerProfileImageAPI(file, token);

// Upload facility image
const [error, result] = await uploadFacilityImageAPI(file, token);
```

## Response Format

### Success Response (201)
```json
{
  "message": "Profile image uploaded successfully",
  "imagePath": "/uploads/profiles/users/[USERID]_[TIMESTAMP].[ext]",
  "user": { /* updated user object */ }
}
```

### Error Responses
- **400**: Invalid file type, size, or missing file
- **401**: Unauthorized
- **404**: User/Facility not found
- **500**: Server error

## Security Features

1. **File Type Validation**: Only image files allowed
2. **Size Limits**: 5MB maximum file size
3. **Authentication**: All endpoints require valid JWT token
4. **Role-Based Access**: Facility uploads restricted to facility managers
5. **File Extension Validation**: Double-check on MIME type and extension
6. **Automatic Cleanup**: Old images deleted to prevent storage bloat

## Implementation Details

### File Upload Utility (`server/utils/fileUpload.ts`)
- Centralized configuration and validation
- Reusable functions for all upload endpoints
- Consistent error handling and security checks

### Database Integration
- User images stored in `User.img` field
- Facility images stored in `Facility.img` field
- Automatic `updatedAt` timestamp updates

### Error Handling
- Comprehensive validation before file processing
- Graceful cleanup on errors
- Detailed error messages for debugging

## Testing

To test the implementation:

1. Start the server: `npm run dev`
2. Use a tool like Postman or create a simple HTML form
3. Send POST request with `multipart/form-data` containing an image file
4. Verify the file is saved in the correct directory with proper naming
5. Check that the database is updated with the new image path
6. Confirm old images are deleted when uploading new ones

## Notes

- Images are served statically via `/uploads` route
- The implementation uses Multer with memory storage for processing
- Files are written to disk using Node.js `fs/promises`
- The naming convention ensures uniqueness and traceability
- All endpoints follow the existing authentication and authorization patterns
