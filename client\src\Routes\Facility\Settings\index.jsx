import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { getFacilityMeAPI } from "../../../api";
import {
  Business as BusinessIcon,
  Settings as SettingsIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { LoadingBarStore } from "../../../data";
import FacilityImageUpload from "../../../components/FacilityImageUpload";

export default function FacilitySettings() {
  const [facility, setFacility] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = LoadingBarStore.useStore();
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    type: "",
    address: "",
    phone: "",
    img: "",
  });

  useEffect(() => {
    loadFacility();
  }, []);

  const loadFacility = async () => {
    try {
      setLoading({ loading: true });
      const token = localStorage.getItem("token");
      const [err, data] = await getFacilityMeAPI(token);

      if (err) {
        toast.error("Failed to load facility information");
        return;
      }

      setFacility(data);
      setFormData({
        name: data.name || "",
        type: data.type || "",
        address: data.address || "",
        phone: data.phone || "",
        img: data.img || "",
      });
    } catch (err) {
      console.error("Facility error:", err);
      toast.error("Failed to load facility information");
    } finally {
      setLoading({ loading: false });
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setLoading({ loading: true });
      const token = localStorage.getItem("token");

      // Note: You'll need to implement updateFacilityAPI in your api/index.js
      // const [err, data] = await updateFacilityAPI(token, formData);

      // For now, just show success message
      toast.success("Facility information updated successfully");
      setEditMode(false);
    } catch (err) {
      console.error("Update error:", err);
      toast.error("Failed to update facility information");
    } finally {
      setSaving(false);
      setLoading({ loading: false });
    }
  };

  const handleCancel = () => {
    setFormData({
      name: facility?.name || "",
      type: facility?.type || "",
      address: facility?.address || "",
      phone: facility?.phone || "",
      img: facility?.img || "",
    });
    setEditMode(false);
  };

  // Handle facility image update
  const handleImageUpdate = (newImagePath, updatedFacility) => {
    setFacility(updatedFacility);
    setFormData(prev => ({
      ...prev,
      img: newImagePath
    }));
  };

  if (!facility) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading facility settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <SettingsIcon className="mr-3 text-blue-600" />
            Facility Settings
          </h1>
          <p className="mt-2 text-gray-600">
            Manage your facility information and branding
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Facility Image Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 text-center">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Facility Logo/Image
              </h3>
              
              {/* Facility Image Upload */}
              <FacilityImageUpload
                currentImage={facility.img}
                onImageUpdate={handleImageUpdate}
                size="large"
                showLabel={false}
                className="mb-4"
              />

              <h2 className="text-xl font-semibold text-gray-800 mb-2">
                {facility.name || "Facility"}
              </h2>
              <p className="text-gray-600 mb-4 capitalize">{facility.type}</p>

              <div className="w-full border-t border-gray-100 pt-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Type</span>
                  <span className="font-medium capitalize">{facility.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Phone</span>
                  <span className="font-medium">{facility.phone || "Not set"}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Facility Information Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-800">
                  Facility Information
                </h3>
                <div className="flex space-x-3">
                  {editMode ? (
                    <>
                      <button
                        onClick={handleSave}
                        disabled={saving}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        <SaveIcon className="mr-2 h-4 w-4" />
                        {saving ? "Saving..." : "Save"}
                      </button>
                      <button
                        onClick={handleCancel}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <CancelIcon className="mr-2 h-4 w-4" />
                        Cancel
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => setEditMode(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <EditIcon className="mr-2 h-4 w-4" />
                      Edit
                    </button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Facility Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    disabled={!editMode}
                    className={`w-full px-4 py-2 rounded-lg border ${
                      editMode
                        ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        : "border-transparent bg-gray-50"
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleInputChange("type", e.target.value)}
                    disabled={!editMode}
                    className={`w-full px-4 py-2 rounded-lg border ${
                      editMode
                        ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        : "border-transparent bg-gray-50"
                    }`}
                  >
                    <option value="hospital">Hospital</option>
                    <option value="clinic">Clinic</option>
                    <option value="cabinet">Cabinet</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <textarea
                    value={formData.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                    disabled={!editMode}
                    rows={3}
                    className={`w-full px-4 py-2 rounded-lg border ${
                      editMode
                        ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        : "border-transparent bg-gray-50"
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    disabled={!editMode}
                    className={`w-full px-4 py-2 rounded-lg border ${
                      editMode
                        ? "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        : "border-transparent bg-gray-50"
                    }`}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
