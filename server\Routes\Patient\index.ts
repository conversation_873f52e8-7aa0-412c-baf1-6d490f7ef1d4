import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { User } from "../../Models/User";
import { QUERY_PAGE_SIZE_LIMIT } from "../../utils/FLAGS";
import { roles } from "../../utils/roles";
import { Folder } from "../../Models/Folder";
import { hashPassword } from "../../utils/passwordHash";
import { logError } from "../../utils/logger";

const router = Router();
router.use(authMiddleware);

// Get patient profile
router.get("/profile", async (req, res) => {
  try {
    const patientId = req.user.id;

    const patient = await User.findById(patientId)
      .select("-password")
      .lean();

    if (!patient) {
      res.status(404).json({ message: "Patient not found" });
      return;
    }

    res.json({ data: patient });
  } catch (error) {
    console.error("Patient profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Update patient profile
router.put("/profile", async (req, res) => {
  try {
    const patientId = req.user.id;
    const { name, email, phone, img } = req.body;

    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (phone) updateData.phone = phone;
    if (img) updateData.img = img;
    updateData.updatedAt = Date.now();

    const updatedUser = await User.findByIdAndUpdate(
      patientId,
      updateData,
      { new: true }
    )
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ message: "Patient not found" });
      return;
    }

    res.json({ data: updatedUser });
  } catch (error) {
    console.error("Update patient profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});
router.post(
  "/",
  forceQuitMiddleware({
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    const { name, cin, email, phone, birthDate, gender } = req.body;
    if (!name || !cin || !email || !phone || !birthDate || !gender) {
      res.status(400).json({
        error: "Name, CIN, email, phone and birthDate are required",
      });
      return;
    }
    const patient = new User({
      name,
      cin,
      email,
      phone,
      gender,
      BirthDay: birthDate,
      role: roles.patient,
      password: hashPassword(cin),
    });
    try {
      await patient.save();
      const folder = await new Folder({
        patient: patient._id,
        notes: "",
        start: Date.now(),
      }).save();
      //@ts-ignore
      patient.folder = folder;
      res.status(201).json(patient);
    } catch (error) {
      console.log("Error creating patient:", error);
      res.status(500).json({ error: "Failed to create patient" });
    }
  }
);

router.get(
  "/search/cin/:cin",
  forceQuitMiddleware({
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    const { cin } = req.params;
    const { page_number = 0 } = req.query;
    if (isNaN(Number(page_number))) {
      res.status(400).json({
        nm: page_number,
        error: "Page number must be a valid number",
      });
      return;
    }
    if (!cin || cin.length < 2) {
      res.status(400).json({
        error: "CIN must be at least 2 characters long",
      });
      return;
    }
    const patients = await User.find({
      cin: { $regex: cin, $options: "i" },
      "profiles.type": roles.patient,
    })
      .skip(Number(page_number) * QUERY_PAGE_SIZE_LIMIT)
      .limit(QUERY_PAGE_SIZE_LIMIT)
      .select({
        name: 1,
        cin: 1,
        email: 1,
        phone: 1,
        BirthDay: 1,
        gender: 1,
        img: 1,
      })
      .populate("folder")
      .lean();
    res.json(patients);
  }
);

router.get(
  "/search/advanced",
  forceQuitMiddleware({
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { name, cin, email, phone, birthDate, exact } = req.query as {
        name: string;
        cin: string;
        email: string;
        phone: string;
        birthDate: string;
        exact: string | undefined;
      };

      const query: any = { "profiles.type": roles.patient };
      if (name) {
        query.name = exact
          ? { $all: name.split(" ").filter((e) => e.trim()) }
          : { $elemMatch: { $regex: name.replace(/ /g, "|"), $options: "i" } };
      }
      if (cin) {
        query.cin = exact ? cin : { $regex: cin, $options: "i" };
      }
      if (email) {
        query.email = exact ? email : { $regex: email, $options: "i" };
      }
      if (phone) {
        query.phone = exact ? phone : { $regex: phone, $options: "i" };
      }
      if (birthDate) {
        const date = new Date(+birthDate);
        if (!isNaN(date.getTime())) {
          query.BirthDay = date;
        } else {
          res.status(400).json({ error: "Invalid birthDate format" });
          return;
        }
      }
      const patients = await User.find(query)
        .limit(QUERY_PAGE_SIZE_LIMIT)
        .select({
          name: 1,
          cin: 1,
          email: 1,
          phone: 1,
          BirthDay: 1,
        })
        .populate("folder")
        .lean();
      if (patients.length === 0) {
        res.status(404).json({ error: "No patients found" });
        return;
      }
      res.json(patients);
    } catch (error) {
      logError("Error searching patients:", error);
      res.status(500).json({ error: "Failed to search patients" });
    }
  }
);
export default router;
