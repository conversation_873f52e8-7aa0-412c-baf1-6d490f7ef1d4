import { Route, Routes, useNavigate } from "react-router-dom";
import "./App.css";
import Login from "./Routes/Login";
import SideBar from "./SideBar";
import { PopUpElement } from "./data";
import { Store } from "react-data-stores";
import { _404, IndexRoute } from "./Routes";
import DoctorRoute from "./Routes/Doctor";
import FacilityRoute from "./Routes/Facility";
import PatientRoute from "./Routes/Patient";
import AdminRoute from "./Routes/Admin";
import SudoRoute from "./Routes/Sudo";
import LoadingBar from "./utils/LoadingBar";
import { Toaster } from "react-hot-toast";
import { PreventBackBtn, useRedirect } from "./utils/costumeHook";

export default function App() {
  const PopUp = PopUpElement.useStore({ setter: false });
  Store.navigateTo = useNavigate();
  useRedirect();
  return (
    <>
      <SideBar />
      <PreventBackBtn />
      <div className="">
        <Routes>
          <Route index element={<IndexRoute />} />
          <Route path="/login" element={<Login />} />
          <Route path="/doctor/*" element={<DoctorRoute />} />
          <Route path="/facility/*" element={<FacilityRoute />} />
          <Route path="/patient/*" element={<PatientRoute />} />
          <Route path="/admin/*" element={<AdminRoute />} />
          <Route path="/sudo/*" element={<SudoRoute />} />
          <Route path="*" element={<_404 />} />
        </Routes>
      </div>
      <LoadingBar />
      <Toaster position="top-right" />
      <PopUp.element />
    </>
  );
}
