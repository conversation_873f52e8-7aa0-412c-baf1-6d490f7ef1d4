import { useState } from "react";
import { userDataStore } from "../data";
import { <PERSON>Bar<PERSON><PERSON>gle, BrandHeader, MenuItem, UserProfile } from "./_components";
import { SectionsByRole } from "./sections";

export default function SideBar() {
  const [sideBarExpanded, setSideBarExpantion] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState("");
  const [userData] = userDataStore.useStore();

  if (!userData.token) return null;

  const Sections = SectionsByRole[userData.data.role];

  return (
    <div
      className={`fixed top-0  h-screen z-1000 transition-left duration-300 ease-in-out ${
        sideBarExpanded ? "left-0" : "left-[-250px]"
      }`}
    >
      <SideBarToggle
        sideBarExpanded={sideBarExpanded}
        setSideBarExpantion={setSideBarExpantion}
      />

      <div className="w-[250px] h-full flex flex-col bg-[var(--secondary-color)] text-white shadow-md">
        <div className="flex-1 overflow-y-auto py-4">
          <BrandHeader />

          <div className="flex flex-col gap-2 px-2">
            {Sections?.map((section) => (
              <MenuItem
                key={section.name}
                section={section}
                selectedMenuItem={selectedMenuItem}
                setSelectedMenuItem={setSelectedMenuItem}
                setSideBarExpantion={setSideBarExpantion}
              />
            ))}
          </div>
        </div>

        <UserProfile userData={userData} />
      </div>
    </div>
  );
}
