# Frontend Profile Image Upload Implementation

This document describes the frontend UI components and implementation for profile image upload functionality across all user types and facility management.

## Overview

The frontend implementation provides intuitive, drag-and-drop image upload functionality with real-time preview, progress indicators, and comprehensive error handling. All components follow the existing design patterns and integrate seamlessly with the backend API.

## Components Created

### 1. ProfileImageUpload Component (`client/src/components/ProfileImageUpload.jsx`)

**Purpose**: Reusable component for user profile image uploads (patients, doctors, admins, sudo users, facility managers)

**Features**:
- Drag and drop file upload
- Click to upload functionality
- Real-time image preview
- Upload progress indicators
- File validation (type, size, format)
- Error handling with user-friendly messages
- Configurable sizes (small, medium, large)
- Automatic old image cleanup

**Props**:
- `currentImage`: Current profile image URL
- `userType`: Type of user ("patient", "doctor", "admin", "sudo", "facilityManager")
- `onImageUpdate`: Callback function when image is updated
- `size`: Component size ("small", "medium", "large")
- `showLabel`: Whether to show the label
- `className`: Additional CSS classes

**Usage Example**:
```jsx
<ProfileImageUpload
  currentImage={profile.img}
  userType="patient"
  onImageUpdate={handleImageUpdate}
  size="medium"
  showLabel={false}
  className="mb-4"
/>
```

### 2. FacilityImageUpload Component (`client/src/components/FacilityImageUpload.jsx`)

**Purpose**: Specialized component for facility logo/image uploads

**Features**:
- Rectangular layout optimized for logos
- Same upload functionality as ProfileImageUpload
- Facility-specific styling and messaging
- Integration with facility image API

**Props**:
- `currentImage`: Current facility image URL
- `onImageUpdate`: Callback function when image is updated
- `size`: Component size ("small", "medium", "large")
- `showLabel`: Whether to show the label
- `className`: Additional CSS classes

## Updated Profile Pages

### 1. Patient Profile (`client/src/Routes/Patient/Profile/index.jsx`)
- Replaced static image display with ProfileImageUpload component
- Added `handleImageUpdate` function to update profile state
- Integrated with patient profile upload API

### 2. Doctor Profile (`client/src/Routes/Doctor/Profile/index.jsx`)
- Replaced static image display with ProfileImageUpload component
- Removed old "Profile Image URL" input field
- Added `handleImageUpdate` function
- Integrated with doctor profile upload API

### 3. Admin Profile (`client/src/Routes/Admin/Profile/index.jsx`)
- Replaced static image display with ProfileImageUpload component
- Added `handleImageUpdate` function
- Integrated with admin profile upload API

### 4. Sudo Profile (`client/src/Routes/Sudo/Profile/index.jsx`)
- Replaced static image display with ProfileImageUpload component
- Added `handleImageUpdate` function
- Integrated with sudo profile upload API

### 5. Facility Manager Profile (`client/src/Routes/Facility/Profile/index.jsx`)
- Replaced static image display with ProfileImageUpload component
- Added `handleImageUpdate` function
- Integrated with facility manager profile upload API

## New Facility Settings Page

### Facility Settings (`client/src/Routes/Facility/Settings/index.jsx`)

**Purpose**: Dedicated page for facility managers to manage facility information and upload facility logos

**Features**:
- Facility image upload using FacilityImageUpload component
- Facility information editing (name, type, address, phone)
- Edit/Save/Cancel functionality
- Responsive grid layout
- Integration with facility image upload API

**Route**: `/facility/settings`

## File Validation

### Frontend Validation
- **File Types**: JPEG, PNG, GIF, WebP
- **File Size**: Maximum 5MB
- **File Extensions**: .jpg, .jpeg, .png, .gif, .webp
- **Real-time validation**: Immediate feedback on invalid files

### Validation Messages
- "File size must be less than 5MB"
- "Only JPEG, PNG, GIF, and WebP images are allowed"
- "Invalid file extension"
- "No file selected"

## User Experience Features

### 1. Drag and Drop
- Visual feedback when dragging files over upload area
- Hover effects and scale animations
- Drop zone highlighting

### 2. Upload Progress
- Loading spinner during upload
- Progress bar at bottom of image container
- Disabled state during upload
- "Uploading..." text feedback

### 3. Image Preview
- Immediate preview of selected image before upload
- Fallback to default icons when no image is present
- Proper aspect ratio handling

### 4. Error Handling
- Toast notifications for success/error messages
- Graceful fallback on upload failures
- Clear error messages for different failure types

### 5. Responsive Design
- Works on desktop, tablet, and mobile devices
- Configurable sizes for different contexts
- Consistent styling across all user types

## Integration with Backend

### API Integration
- Uses existing `uploadUserProfileImageAPI` for user profiles
- Uses `uploadFacilityManagerProfileImageAPI` for facility manager profiles
- Uses `uploadFacilityImageAPI` for facility images
- Automatic token handling from localStorage
- Proper error handling and response processing

### State Management
- Updates local component state immediately
- Calls parent component callbacks to update profile data
- Integrates with global loading state (LoadingBarStore)
- Maintains form data consistency

## Styling and Design

### Design System Compliance
- Follows existing Tailwind CSS patterns
- Consistent with current UI components
- Uses Material-UI icons for consistency
- Maintains existing color schemes and spacing

### Accessibility
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- High contrast support

## Testing Checklist

### Functional Testing
- [ ] File upload works for all user types
- [ ] Drag and drop functionality
- [ ] File validation (type, size, extension)
- [ ] Progress indicators during upload
- [ ] Error handling for failed uploads
- [ ] Success messages and state updates
- [ ] Image preview functionality
- [ ] Old image cleanup

### UI/UX Testing
- [ ] Responsive design on different screen sizes
- [ ] Hover effects and animations
- [ ] Loading states and disabled states
- [ ] Toast notifications
- [ ] Consistent styling across pages

### Integration Testing
- [ ] API integration with backend endpoints
- [ ] Token authentication
- [ ] Profile state updates
- [ ] Navigation between pages
- [ ] Error boundary handling

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance Considerations

- Image preview uses FileReader API for client-side processing
- File validation happens before upload to reduce server load
- Automatic cleanup of file input after upload
- Optimized re-renders using React best practices

## Future Enhancements

- Image cropping functionality
- Multiple image upload support
- Image compression before upload
- Advanced image editing tools
- Bulk upload capabilities
