export default function SideBarToggle({ sideBarExpanded, setSideBarExpantion }) {
  return (
    <span
      className="absolute top-4 right-[-2.5rem] w-8 h-8 bg-[var(--secondary-color)] text-white rounded-full flex items-center justify-center cursor-pointer shadow-md"
      onClick={() => setSideBarExpantion((prev) => !prev)}
    >
      <i className="fa-solid fa-arrow-right"></i>
    </span>
  );
}
