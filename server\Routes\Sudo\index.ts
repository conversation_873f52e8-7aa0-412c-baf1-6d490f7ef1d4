import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { User } from "../../Models/User";

const router = Router();
router.use(authMiddleware);

// Get sudo profile
router.get("/profile", async (req, res) => {
  try {
    const sudoId = req.user.id;

    const sudo = await User.findById(sudoId)
      .select("-password")
      .lean();

    if (!sudo) {
      res.status(404).json({ message: "Sudo user not found" });
      return;
    }

    res.json({ data: sudo });
  } catch (error) {
    console.error("Sudo profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Update sudo profile
router.put("/profile", async (req, res) => {
  try {
    const sudoId = req.user.id;
    const { name, email, phone, img } = req.body;

    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (phone) updateData.phone = phone;
    if (img) updateData.img = img;
    updateData.updatedAt = Date.now();

    const updatedUser = await User.findByIdAndUpdate(
      sudoId,
      updateData,
      { new: true }
    )
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ message: "Sudo user not found" });
      return;
    }

    res.json({ data: updatedUser });
  } catch (error) {
    console.error("Update sudo profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

export default router;
