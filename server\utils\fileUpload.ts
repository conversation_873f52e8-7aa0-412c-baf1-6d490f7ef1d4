import * as fs from "fs/promises";
import path from "path";

// eslint-disable-next-line @typescript-eslint/no-var-requires
const multer: any = require("multer");

// File upload configuration
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_MIME_TYPES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  MAX_FILES: 1, // Profile images are single files
};

// Create multer instance with disk storage for profile images
export const createProfileImageUpload = () => {
  return multer({ 
    storage: multer.memoryStorage(),
    limits: {
      fileSize: UPLOAD_CONFIG.MAX_FILE_SIZE,
      files: UPLOAD_CONFIG.MAX_FILES
    },
    fileFilter: (req: any, file: any, cb: any) => {
      // Check file type
      if (!UPLOAD_CONFIG.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
        return cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'));
      }
      
      // Check file extension
      const ext = path.extname(file.originalname).toLowerCase();
      if (!UPLOAD_CONFIG.ALLOWED_EXTENSIONS.includes(ext)) {
        return cb(new Error('Invalid file extension. Only .jpg, .jpeg, .png, .gif, and .webp files are allowed.'));
      }
      
      cb(null, true);
    }
  });
};

// Validate uploaded file
export const validateUploadedFile = (file: any): string | null => {
  if (!file) {
    return "No file provided";
  }
  
  if (!file.buffer || file.buffer.length === 0) {
    return "File is empty";
  }
  
  if (file.size > UPLOAD_CONFIG.MAX_FILE_SIZE) {
    return `File size exceeds limit of ${UPLOAD_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`;
  }
  
  if (!UPLOAD_CONFIG.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
    return "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.";
  }
  
  return null; // No validation errors
};

// Generate filename using the required format: [ID]_[UNIXTIMESTAMP].[ext]
export const generateProfileImageFilename = (id: string, originalFilename: string): string => {
  const timestamp = Date.now();
  const ext = path.extname(originalFilename).toLowerCase() || '.jpg';
  return `${id}_${timestamp}${ext}`;
};

// Save file to disk and return the public path
export const saveProfileImage = async (
  file: any, 
  filename: string, 
  subDirectory: 'users' | 'facilities'
): Promise<string> => {
  const uploadsRoot = path.join(__dirname, "..", "uploads");
  const profilesDir = path.join(uploadsRoot, "profiles");
  const targetDir = path.join(profilesDir, subDirectory);
  
  // Ensure directory exists
  await fs.mkdir(targetDir, { recursive: true });
  
  // Save file
  const absPath = path.join(targetDir, filename);
  await fs.writeFile(absPath, file.buffer);
  
  // Return public path
  return `/uploads/profiles/${subDirectory}/${filename}`;
};

// Delete old profile image file if it exists
export const deleteOldProfileImage = async (oldImagePath: string | null): Promise<void> => {
  if (!oldImagePath || !oldImagePath.startsWith('/uploads/profiles/')) {
    return; // Not a local file or no file to delete
  }
  
  try {
    const uploadsRoot = path.join(__dirname, "..", "uploads");
    const filePath = path.join(uploadsRoot, oldImagePath.replace('/uploads/', ''));
    await fs.unlink(filePath);
  } catch (error) {
    // File might not exist, which is fine
    console.warn('Could not delete old profile image:', oldImagePath, error);
  }
};
