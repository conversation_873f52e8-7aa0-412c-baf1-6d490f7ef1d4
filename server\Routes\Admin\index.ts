import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { User } from "../../Models/User";
import {
  createProfileImageUpload,
  validateUploadedFile,
  generateProfileImageFilename,
  saveProfileImage,
  deleteOldProfileImage
} from "../../utils/fileUpload";

const router = Router();
router.use(authMiddleware);

// Create multer upload instance for profile images
const profileImageUpload = createProfileImageUpload();

// Get admin profile
router.get("/profile", async (req, res) => {
  try {
    const adminId = req.user.id;

    const admin = await User.findById(adminId)
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!admin) {
      res.status(404).json({ message: "Admin not found" });
      return;
    }

    res.json({ data: admin });
  } catch (error) {
    console.error("Admin profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Update admin profile
router.put("/profile", async (req, res) => {
  try {
    const adminId = req.user.id;
    const { name, email, phone, img } = req.body;

    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (phone) updateData.phone = phone;
    if (img) updateData.img = img;
    updateData.updatedAt = Date.now();

    const updatedUser = await User.findByIdAndUpdate(
      adminId,
      updateData,
      { new: true }
    )
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ message: "Admin not found" });
      return;
    }

    res.json({ data: updatedUser });
  } catch (error) {
    console.error("Update admin profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Upload admin profile image
router.post("/profile/upload", profileImageUpload.single("image"), async (req, res) => {
  try {
    const adminId = req.user.id;
    const file = (req as any).file;

    // Validate the uploaded file
    const validationError = validateUploadedFile(file);
    if (validationError) {
      res.status(400).json({ error: validationError });
      return;
    }

    // Get current user to check for existing image
    const currentUser = await User.findById(adminId).select("img").lean();
    if (!currentUser) {
      res.status(404).json({ error: "Admin not found" });
      return;
    }

    // Generate filename and save file
    const filename = generateProfileImageFilename(adminId, file.originalname);
    const publicPath = await saveProfileImage(file, filename, 'users');

    // Update user's img field in database
    const updatedUser = await User.findByIdAndUpdate(
      adminId,
      {
        img: publicPath,
        updatedAt: Date.now()
      },
      { new: true }
    )
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ error: "Admin not found" });
      return;
    }

    // Delete old image file if it exists
    if (currentUser.img) {
      await deleteOldProfileImage(currentUser.img);
    }

    res.status(201).json({
      message: "Profile image uploaded successfully",
      imagePath: publicPath,
      user: updatedUser
    });

  } catch (error) {
    console.error("Upload admin profile image error:", error);
    res.status(500).json({ error: "Failed to upload profile image" });
  }
});

export default router;
