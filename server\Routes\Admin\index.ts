import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { User } from "../../Models/User";

const router = Router();
router.use(authMiddleware);

// Get admin profile
router.get("/profile", async (req, res) => {
  try {
    const adminId = req.user.id;

    const admin = await User.findById(adminId)
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!admin) {
      res.status(404).json({ message: "Admin not found" });
      return;
    }

    res.json({ data: admin });
  } catch (error) {
    console.error("Admin profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// Update admin profile
router.put("/profile", async (req, res) => {
  try {
    const adminId = req.user.id;
    const { name, email, phone, img } = req.body;

    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (phone) updateData.phone = phone;
    if (img) updateData.img = img;
    updateData.updatedAt = Date.now();

    const updatedUser = await User.findByIdAndUpdate(
      adminId,
      updateData,
      { new: true }
    )
      .populate("profiles.hospital", "name")
      .select("-password")
      .lean();

    if (!updatedUser) {
      res.status(404).json({ message: "Admin not found" });
      return;
    }

    res.json({ data: updatedUser });
  } catch (error) {
    console.error("Update admin profile error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

export default router;
