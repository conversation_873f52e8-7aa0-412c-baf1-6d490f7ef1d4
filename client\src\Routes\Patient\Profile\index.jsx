import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { getPatientProfileAPI, updatePatientProfileAPI } from "../../../api";
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { LoadingBarStore } from "../../../data";
import { formatDate } from "../../../utils/dateFormater";
import ProfileImageUpload from "../../../components/ProfileImageUpload";

export default function PatientProfile() {
  const [profile, setProfile] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = LoadingBarStore.useStore();
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: [],
    email: "",
    phone: "",
    img: "",
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading({ loading: true });
      const token = localStorage.getItem("token");
      const [err, data] = await getPatientProfileAPI(token);

      if (err) {
        toast.error("Failed to load profile");
        return;
      }

      setProfile(data.data);
      setFormData({
        name: data.data.name || [],
        email: data.data.email || "",
        phone: data.data.phone || "",
        img: data.data.img || "",
      });
    } catch (err) {
      console.error("Profile error:", err);
      toast.error("Failed to load profile");
    } finally {
      setLoading({ loading: false });
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNameChange = (index, value) => {
    const newName = [...formData.name];
    newName[index] = value;
    setFormData((prev) => ({
      ...prev,
      name: newName,
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setLoading({ loading: true });
      const token = localStorage.getItem("token");

      const [err, data] = await updatePatientProfileAPI(token, formData);

      if (err) {
        toast.error("Failed to update profile");
        setLoading({ loading: false });
        return;
      }

      setProfile(data.data);
      toast.success("Profile updated successfully");
      setLoading({ loading: false });
      setEditMode(false);
    } catch (err) {
      console.error("Update error:", err);
      toast.error("Failed to update profile");
    } finally {
      setSaving(false);
    }
  };

  // Handle profile image update
  const handleImageUpdate = (newImagePath, updatedUser) => {
    setProfile(updatedUser);
    setFormData(prev => ({
      ...prev,
      img: newImagePath
    }));
  };

  const handleCancel = () => {
    setFormData({
      name: profile?.name || [],
      email: profile?.email || "",
      phone: profile?.phone || "",
      img: profile?.img || "",
    });
    setEditMode(false);
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Patient Profile</h1>
          <p className="mt-2 text-gray-600">
            Manage your personal information and medical records
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Summary Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 text-center">
              {/* Profile Image Upload */}
              <ProfileImageUpload
                currentImage={profile.img}
                userType="patient"
                onImageUpdate={handleImageUpdate}
                size="medium"
                showLabel={false}
                className="mb-4"
              />

              <h2 className="text-xl font-semibold text-gray-800 mb-2">
                {profile.name?.join(" ") || "Patient"}
              </h2>
              <p className="text-gray-600 mb-4">{profile.email}</p>

              <div className="w-full border-t border-gray-100 pt-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Current Profile</span>
                  <span className="font-medium">Patient</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Gender</span>
                  <span className="font-medium">
                    {profile?.gender === "M" ? "Male" : "Female"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">CIN</span>
                  <span className="font-medium">{profile?.cin || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Birth Date</span>
                  <span className="font-medium">
                    {profile?.BirthDay ? formatDate(profile.BirthDay) : "N/A"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Phone</span>
                  <span className="font-medium">{profile?.phone || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Member Since</span>
                  <span className="font-medium">
                    {formatDate(profile?.createdAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Edit Profile Card */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-xl font-semibold text-gray-800">
                  Personal Information
                </h2>
                {!editMode ? (
                  <button
                    onClick={() => setEditMode(true)}
                    className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <EditIcon className="mr-2" />
                    Edit Profile
                  </button>
                ) : (
                  <div className="flex gap-3">
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-70"
                    >
                      <SaveIcon className="mr-2" />
                      {saving ? "Saving..." : "Save"}
                    </button>
                    <button
                      onClick={handleCancel}
                      className="flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                    >
                      <CancelIcon className="mr-2" />
                      Cancel
                    </button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  {editMode ? (
                    <input
                      type="text"
                      value={formData.name[0] || ""}
                      onChange={(e) => handleNameChange(0, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{profile.name?.[0] || "N/A"}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  {editMode ? (
                    <input
                      type="text"
                      value={formData.name[1] || ""}
                      onChange={(e) => handleNameChange(1, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{profile.name?.[1] || "N/A"}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  {editMode ? (
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{profile.email || "N/A"}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  {editMode ? (
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">{profile.phone || "N/A"}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
