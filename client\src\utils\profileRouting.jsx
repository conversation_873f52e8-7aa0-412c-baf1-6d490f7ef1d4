import { roles } from "../data";

/**
 * Get the profile route for a given user role
 * @param {string} userRole - The user's role (from roles object)
 * @returns {string} - The profile route path for the user's role
 */
export const getProfileRouteForRole = (userRole) => {
  const profileRoutes = {
    [roles.doctor]: "/doctor/profile",
    [roles.facilityManager]: "/facility/profile", 
    [roles.patient]: "/patient/profile",
    [roles.admin]: "/admin/profile",
    [roles.sudo]: "/sudo/profile",
  };

  return profileRoutes[userRole] || "/doctor/profile"; // fallback to doctor profile
};

/**
 * Get the profile icon for a given user role
 * @param {string} userRole - The user's role (from roles object)
 * @returns {JSX.Element} - The appropriate icon for the user's role
 */
export const getProfileIconForRole = (userRole) => {
  const profileIcons = {
    [roles.doctor]: <i className="fa-solid fa-user-doctor"></i>,
    [roles.facilityManager]: <i className="fa-solid fa-clipboard-list"></i>,
    [roles.patient]: <i className="fa-solid fa-user-injured"></i>,
    [roles.admin]: <i className="fa-solid fa-user-shield"></i>,
    [roles.sudo]: <i className="fa-solid fa-user-cog"></i>,
  };

  return profileIcons[userRole] || <i className="fa-solid fa-user"></i>; // fallback to generic user icon
};
