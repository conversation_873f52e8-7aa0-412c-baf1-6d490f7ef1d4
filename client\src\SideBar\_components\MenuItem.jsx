export default function MenuItem({ 
  section, 
  selectedMenuItem, 
  setSelectedMenuItem, 
  setSideBarExpantion 
}) {
  if (section.items) {
    // Expandable menu item with sub-items
    return (
      <div
        className={`flex flex-col ${
          selectedMenuItem === section.name ? "bg-white/10" : ""
        }`}
      >
        <div
          className="flex items-center justify-between px-4 py-3 cursor-pointer hover:bg-white/5"
          onClick={() =>
            setSelectedMenuItem((prev) =>
              prev !== section.name ? section.name : ""
            )
          }
        >
          <div className="flex items-center gap-3">
            <span className="w-5 text-center">{section.icon}</span>
            <span>{section.name}</span>
          </div>
          <i
            className={`fa-solid fa-caret-${
              selectedMenuItem === section.name ? "up" : "down"
            }`}
          />
        </div>

        <div
          className={`flex flex-col overflow-hidden transition-[max-height] duration-300 ease-in-out px-4 ${
            selectedMenuItem === section.name
              ? "max-h-[500px]"
              : "max-h-0"
          }`}
        >
          {section.items.map((item) => (
            <div
              key={item.name}
              className="flex items-center gap-3 py-2 pl-6 pr-4 cursor-pointer hover:bg-white/5"
              {...item.props}
            >
              <span className="w-5 text-center">{item.icon}</span>
              <span>{item.name}</span>
            </div>
          ))}
        </div>
      </div>
    );
  } else {
    // Simple menu item
    return (
      <div
        className="flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-white/5"
        {...section.props}
        onClick={(e) => {
          section.props.onClick(e);
          setSideBarExpantion((prev) => !prev);
        }}
      >
        <span className="w-5 text-center">{section.icon}</span>
        <span>{section.name}</span>
      </div>
    );
  }
}
