import { useState, useRef } from "react";
import { toast } from "react-hot-toast";
import { 
  PhotoCamera as PhotoCameraIcon,
  CloudUpload as CloudUploadIcon,
  Business as BusinessIcon
} from "@mui/icons-material";
import { LoadingBarStore } from "../data";
import { uploadFacilityImageAPI } from "../api";

// File validation constants (matching backend)
const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
};

export default function FacilityImageUpload({ 
  currentImage, 
  onImageUpdate,
  size = "large", // "small", "medium", "large"
  showLabel = true,
  className = ""
}) {
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Size configurations
  const sizeConfig = {
    small: { container: "w-20 h-20", icon: "text-2xl", button: "text-xs" },
    medium: { container: "w-32 h-32", icon: "text-3xl", button: "text-sm" },
    large: { container: "w-40 h-40", icon: "text-4xl", button: "text-base" }
  };

  const config = sizeConfig[size] || sizeConfig.large;

  // Validate file before upload
  const validateFile = (file) => {
    if (!file) {
      return "No file selected";
    }

    if (file.size > UPLOAD_CONFIG.MAX_FILE_SIZE) {
      return `File size must be less than ${UPLOAD_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`;
    }

    if (!UPLOAD_CONFIG.ALLOWED_TYPES.includes(file.type)) {
      return "Only JPEG, PNG, GIF, and WebP images are allowed";
    }

    const extension = file.name.toLowerCase().split('.').pop();
    if (!UPLOAD_CONFIG.ALLOWED_EXTENSIONS.includes(`.${extension}`)) {
      return "Invalid file extension";
    }

    return null;
  };

  // Handle file selection
  const handleFileSelect = (file) => {
    const validationError = validateFile(file);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
    };
    reader.readAsDataURL(file);

    // Upload file
    uploadFile(file);
  };

  // Upload file to server
  const uploadFile = async (file) => {
    try {
      setUploading(true);
      LoadingBarStore.setCurrent({ loading: true });

      const token = localStorage.getItem("token");
      const [error, result] = await uploadFacilityImageAPI(file, token);

      if (error) {
        console.error("Upload error:", error);
        toast.error("Failed to upload facility image");
        setPreview(null);
        return;
      }

      toast.success("Facility image updated successfully");
      
      // Update parent component with new image
      if (onImageUpdate) {
        onImageUpdate(result.imagePath, result.facility);
      }

      setPreview(null); // Clear preview since we now have the actual image

    } catch (err) {
      console.error("Upload error:", err);
      toast.error("Failed to upload facility image");
      setPreview(null);
    } finally {
      setUploading(false);
      LoadingBarStore.setCurrent({ loading: false });
      
      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle file input change
  const handleInputChange = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Get current display image
  const displayImage = preview || currentImage;

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {showLabel && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Facility Logo/Image
        </label>
      )}

      {/* Image Display and Upload Area */}
      <div
        className={`relative ${config.container} rounded-lg overflow-hidden border-2 border-dashed border-gray-300 cursor-pointer transition-all duration-200 ${
          dragOver ? 'border-blue-500 bg-blue-50 scale-105' : 'hover:border-blue-400 hover:bg-gray-50'
        } ${uploading ? 'opacity-50' : ''}`}
        onClick={() => !uploading && fileInputRef.current?.click()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {displayImage ? (
          <img
            src={displayImage}
            alt="Facility"
            className="w-full h-full object-contain bg-white"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
            <BusinessIcon className={`text-white ${config.icon}`} />
          </div>
        )}

        {/* Upload Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
          {uploading ? (
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          ) : (
            <PhotoCameraIcon className="text-white text-2xl" />
          )}
        </div>

        {/* Upload Progress Indicator */}
        {uploading && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
            <div className="h-full bg-blue-500 animate-pulse"></div>
          </div>
        )}
      </div>

      {/* Upload Button */}
      <div className="flex flex-col items-center space-y-2">
        <button
          type="button"
          onClick={() => !uploading && fileInputRef.current?.click()}
          disabled={uploading}
          className={`inline-flex items-center px-4 py-2 border border-transparent ${config.button} font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200`}
        >
          {uploading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Uploading...
            </>
          ) : (
            <>
              <CloudUploadIcon className="mr-2 h-4 w-4" />
              {currentImage ? 'Change Logo' : 'Upload Logo'}
            </>
          )}
        </button>

        <p className="text-xs text-gray-500 text-center">
          JPEG, PNG, GIF, WebP up to 5MB<br />
          Recommended: Square or landscape format
        </p>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
        onChange={handleInputChange}
        className="hidden"
        disabled={uploading}
      />
    </div>
  );
}
