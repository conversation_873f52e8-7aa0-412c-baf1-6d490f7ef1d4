import { roles } from "../../data";
import { Store } from "react-data-stores";

export const SectionsByRole = {
  [roles.doctor]: [
    {
      name: "Dashboard",
      icon: <i className="fa-solid fa-gauge"></i>,
      props: {
        onClick: () => Store.navigateTo("/doctor/dashboard"),
        style: { cursor: "pointer" },
      },
    },
    {
      name: "Patients Folders",
      icon: <i className="fa-solid fa-folder"></i>,
      props: {
        onClick: () => Store.navigateTo("/doctor/visits"),
        style: { cursor: "pointer" },
      },
    },
    {
      name: "Appointements",
      icon: <i className="fa-solid fa-calendar-alt"></i>,
      props: {
        onClick: () => Store.navigateTo("/doctor"),
        style: { cursor: "pointer" },
      },
    },
  ],
  [roles.facilityManager]: [
    {
      name: "Dashboard",
      icon: <i className="fa-solid fa-gauge"></i>,
      props: {
        onClick: () => Store.navigateTo("/facility/dashboard"),
        style: { cursor: "pointer" },
      },
    },
    {
      name: "Workers",
      icon: <i className="fa-solid fa-folder"></i>,
      props: {
        onClick: () => Store.navigateTo("/facility/workers"),
        style: { cursor: "pointer" },
      },
    },
  ],
};
